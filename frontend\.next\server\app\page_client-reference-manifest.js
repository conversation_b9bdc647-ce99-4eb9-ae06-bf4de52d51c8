globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"2283":{"*":{"id":"4193","name":"*","chunks":[],"async":false}},"3621":{"*":{"id":"7301","name":"*","chunks":[],"async":false}},"4235":{"*":{"id":"6159","name":"*","chunks":[],"async":false}},"4754":{"*":{"id":"8416","name":"*","chunks":[],"async":false}},"6262":{"*":{"id":"7342","name":"*","chunks":[],"async":false}},"6867":{"*":{"id":"5405","name":"*","chunks":[],"async":false}},"7890":{"*":{"id":"4078","name":"*","chunks":[],"async":false}},"9287":{"*":{"id":"1573","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":4754,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":4754,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":6262,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":6262,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":7890,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":7890,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":2283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":9287,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":9287,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":6867,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":6867,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":3621,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":3621,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":4235,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":4235,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":247,"name":"*","chunks":["177","static/chunks/app/layout-b1309b80ffca40f7.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":8189,"name":"*","chunks":["177","static/chunks/app/layout-b1309b80ffca40f7.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\globals.css":{"id":6002,"name":"*","chunks":["177","static/chunks/app/layout-b1309b80ffca40f7.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/f30152c0704fba31.css"}],"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\Arablms\\frontend\\src\\app\\page":[]},"rscModuleMapping":{"2283":{"*":{"id":"7319","name":"*","chunks":[],"async":false}},"3621":{"*":{"id":"5543","name":"*","chunks":[],"async":false}},"4235":{"*":{"id":"2241","name":"*","chunks":[],"async":false}},"4754":{"*":{"id":"9782","name":"*","chunks":[],"async":false}},"6002":{"*":{"id":"485","name":"*","chunks":[],"async":false}},"6262":{"*":{"id":"3552","name":"*","chunks":[],"async":false}},"6867":{"*":{"id":"868","name":"*","chunks":[],"async":false}},"7890":{"*":{"id":"708","name":"*","chunks":[],"async":false}},"9287":{"*":{"id":"2079","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"2283":{"*":{"id":"4193","name":"*","chunks":[],"async":false}},"3621":{"*":{"id":"7301","name":"*","chunks":[],"async":false}},"4235":{"*":{"id":"6159","name":"*","chunks":[],"async":false}},"4754":{"*":{"id":"8416","name":"*","chunks":[],"async":false}},"6262":{"*":{"id":"7342","name":"*","chunks":[],"async":false}},"6867":{"*":{"id":"5405","name":"*","chunks":[],"async":false}},"7890":{"*":{"id":"4078","name":"*","chunks":[],"async":false}},"9287":{"*":{"id":"1573","name":"*","chunks":[],"async":false}}}}